# 滴答清单MCP服务 -> Node.js uniCloud重构方案

## 项目概述

将现有的滴答清单MCP（Model Context Protocol）服务重构为基于uniCloud的Node.js REST API服务，集成AI能力进行智能意图识别和任务处理。

## 架构转换对比

### 当前架构（Python MCP）
```
用户 -> MCP客户端 -> MCP协议 -> Python服务 -> 滴答清单API
```

### 目标架构（Node.js uniCloud + AI）
```
用户 -> HTTP请求 -> uniCloud云函数 -> AI意图识别 -> 滴答清单API
```

## 核心设计理念

### 1. AI驱动的意图识别
- **替代硬编码规则**：使用AI模型（如通义千问、文心一言等）进行自然语言理解
- **上下文感知**：AI能够理解复杂的任务描述和用户意图
- **动态学习**：基于用户交互历史优化意图识别准确性

### 2. uniCloud云函数架构
- **serverless部署**：无需管理服务器，自动扩缩容
- **统一开发体验**：与uni-app生态无缝集成
- **成本优化**：按需付费，降低运营成本

## 技术栈选择

### 后端技术栈
- **运行环境**：uniCloud (阿里云/腾讯云)
- **开发语言**：Node.js
- **AI服务**：
  - 阿里云通义千问API
  - 腾讯云混元API  
  - 或OpenAI API
- **数据库**：uniCloud DB (MongoDB)
- **文件存储**：uniCloud Storage

### AI集成方案
- **意图识别**：使用大语言模型进行自然语言理解
- **实体提取**：AI提取任务标题、时间、优先级等信息
- **智能补全**：基于历史数据智能补全任务信息

## 详细实现方案

### 1. uniCloud项目结构

```
uniCloud-aliyun/
├── cloudfunctions/
│   ├── intent-analysis/          # AI意图识别云函数
│   ├── task-management/          # 任务管理云函数
│   ├── project-management/       # 项目管理云函数
│   ├── analytics/               # 统计分析云函数
│   ├── goal-management/         # 目标管理云函数
│   └── common/                  # 公共模块
│       ├── dida-api-client.js   # 滴答清单API客户端
│       ├── ai-service.js        # AI服务封装
│       └── utils.js             # 工具函数
├── database/
│   ├── db_init.json            # 数据库初始化
│   └── schema/                 # 数据表结构
└── storage/                    # 文件存储配置
```

### 2. AI意图识别云函数

```javascript
// cloudfunctions/intent-analysis/index.js
'use strict';

const { AIService } = require('../common/ai-service');
const { DidaApiClient } = require('../common/dida-api-client');

exports.main = async (event, context) => {
  const { userInput, authInfo } = event;
  
  try {
    // 初始化AI服务
    const aiService = new AIService();
    
    // AI意图识别
    const intentAnalysis = await aiService.analyzeIntent(userInput);
    
    // 根据意图执行相应操作
    const result = await executeIntent(intentAnalysis, authInfo);
    
    return {
      success: true,
      data: result,
      analysis: intentAnalysis
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

async function executeIntent(analysis, authInfo) {
  const { intent, entities, confidence } = analysis;
  
  if (confidence < 0.7) {
    return {
      needClarification: true,
      message: "我不太确定您的意图，能否更详细地描述一下？",
      suggestedActions: generateSuggestions(analysis)
    };
  }
  
  const didaClient = new DidaApiClient(authInfo);
  await didaClient.initialize();
  
  switch (intent) {
    case 'CREATE_TASK':
      return await createTaskWithAI(entities, didaClient);
    case 'QUERY_TASKS':
      return await queryTasksWithAI(entities, didaClient);
    case 'UPDATE_TASK':
      return await updateTaskWithAI(entities, didaClient);
    case 'ANALYTICS':
      return await generateAnalytics(entities, didaClient);
    default:
      return { message: "抱歉，我暂时无法处理这个请求" };
  }
}
```

### 3. AI服务封装

```javascript
// cloudfunctions/common/ai-service.js
'use strict';

const axios = require('axios');

class AIService {
  constructor() {
    this.apiKey = process.env.AI_API_KEY;
    this.baseURL = process.env.AI_BASE_URL;
  }
  
  async analyzeIntent(userInput) {
    const prompt = this.buildIntentPrompt(userInput);
    
    const response = await axios.post(`${this.baseURL}/chat/completions`, {
      model: "qwen-plus",
      messages: [
        {
          role: "system",
          content: "你是一个专业的任务管理助手，能够理解用户的自然语言输入并识别其意图。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    return this.parseAIResponse(response.data.choices[0].message.content);
  }
  
  buildIntentPrompt(userInput) {
    return `
请分析以下用户输入，识别其意图并提取相关实体信息：

用户输入："${userInput}"

请以JSON格式返回分析结果，包含以下字段：
{
  "intent": "意图类型(CREATE_TASK/QUERY_TASKS/UPDATE_TASK/DELETE_TASK/ANALYTICS/CREATE_PROJECT)",
  "confidence": "置信度(0-1)",
  "entities": {
    "taskTitle": "任务标题",
    "dueDate": "截止日期(YYYY-MM-DD格式)",
    "priority": "优先级(1-5)",
    "projectName": "项目名称",
    "tags": ["标签数组"],
    "description": "任务描述"
  },
  "reasoning": "分析推理过程"
}

意图类型说明：
- CREATE_TASK: 创建新任务
- QUERY_TASKS: 查询任务
- UPDATE_TASK: 更新任务状态或信息
- DELETE_TASK: 删除任务
- ANALYTICS: 统计分析请求
- CREATE_PROJECT: 创建项目

请确保返回有效的JSON格式。
    `;
  }
  
  parseAIResponse(aiResponse) {
    try {
      // 提取JSON部分
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('AI响应格式错误');
    } catch (error) {
      console.error('解析AI响应失败:', error);
      return {
        intent: 'UNKNOWN',
        confidence: 0,
        entities: {},
        reasoning: 'AI响应解析失败'
      };
    }
  }
  
  async enhanceTaskInfo(taskData) {
    const prompt = `
请帮助完善以下任务信息：

原始任务信息：
${JSON.stringify(taskData, null, 2)}

请基于任务标题和描述，智能推荐：
1. 合适的优先级(1-5)
2. 可能的标签
3. 预估完成时间
4. 相关的项目分类建议

返回JSON格式：
{
  "recommendedPriority": 优先级,
  "suggestedTags": ["标签1", "标签2"],
  "estimatedDuration": "预估时长",
  "projectSuggestion": "项目建议",
  "reasoning": "推荐理由"
}
    `;
    
    const response = await this.callAI(prompt);
    return this.parseAIResponse(response);
  }
  
  async callAI(prompt) {
    const response = await axios.post(`${this.baseURL}/chat/completions`, {
      model: "qwen-plus",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3
    }, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data.choices[0].message.content;
  }
}

module.exports = { AIService };
```

### 4. 滴答清单API客户端

```javascript
// cloudfunctions/common/dida-api-client.js
'use strict';

const axios = require('axios');

class DidaApiClient {
  constructor(authInfo) {
    this.baseURL = 'https://api.dida365.com';
    this.authInfo = authInfo;
    this.token = null;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DidaTodoList-uniCloud/1.0'
      }
    });
    
    this.setupInterceptors();
  }
  
  async initialize() {
    if (this.authInfo.token) {
      this.token = this.authInfo.token;
    } else {
      await this.login();
    }
    this.client.defaults.headers.common['Cookie'] = `t=${this.token}`;
  }
  
  async login() {
    const loginData = {
      username: this.authInfo.email || this.authInfo.phone,
      password: this.authInfo.password
    };
    
    const response = await this.client.post('/api/v2/user/signon', loginData);
    this.token = response.data.token;
    return this.token;
  }
  
  setupInterceptors() {
    this.client.interceptors.response.use(
      response => response,
      async error => {
        if (error.response?.status === 401) {
          await this.login();
          return this.client.request(error.config);
        }
        throw error;
      }
    );
  }
  
  // 任务管理方法
  async createTask(taskData) {
    return this.post('/api/v2/batch/task', {
      add: [taskData]
    });
  }
  
  async getTasks(projectId = null) {
    const endpoint = projectId ? 
      `/api/v2/project/${projectId}/data` : 
      '/api/v2/batch/check/0';
    return this.get(endpoint);
  }
  
  async updateTask(taskId, updates) {
    return this.post('/api/v2/batch/task', {
      update: [{ id: taskId, ...updates }]
    });
  }
  
  async deleteTask(taskId) {
    return this.post('/api/v2/batch/task', {
      delete: [taskId]
    });
  }
  
  // 项目管理方法
  async getProjects() {
    const response = await this.get('/api/v2/batch/check/0');
    return response.projectProfiles || [];
  }
  
  async createProject(projectData) {
    return this.post('/api/v2/batch/project', {
      add: [projectData]
    });
  }
  
  // 基础HTTP方法
  async get(endpoint) {
    const response = await this.client.get(endpoint);
    return response.data;
  }
  
  async post(endpoint, data) {
    const response = await this.client.post(endpoint, data);
    return response.data;
  }
  
  async put(endpoint, data) {
    const response = await this.client.put(endpoint, data);
    return response.data;
  }
  
  async delete(endpoint) {
    const response = await this.client.delete(endpoint);
    return response.data;
  }
}

module.exports = { DidaApiClient };
```

### 5. 任务管理云函数

```javascript
// cloudfunctions/task-management/index.js
'use strict';

const { DidaApiClient } = require('../common/dida-api-client');
const { AIService } = require('../common/ai-service');

exports.main = async (event, context) => {
  const { action, data, authInfo } = event;
  
  try {
    const didaClient = new DidaApiClient(authInfo);
    await didaClient.initialize();
    
    const aiService = new AIService();
    
    switch (action) {
      case 'create':
        return await createTaskWithAI(data, didaClient, aiService);
      case 'list':
        return await listTasks(data, didaClient);
      case 'update':
        return await updateTask(data, didaClient);
      case 'delete':
        return await deleteTask(data, didaClient);
      default:
        throw new Error('不支持的操作');
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

async function createTaskWithAI(taskData, didaClient, aiService) {
  // 使用AI增强任务信息
  const enhancement = await aiService.enhanceTaskInfo(taskData);
  
  // 合并AI建议和用户输入
  const enhancedTask = {
    ...taskData,
    priority: taskData.priority || enhancement.recommendedPriority,
    tags: [...(taskData.tags || []), ...enhancement.suggestedTags],
    content: taskData.content || `预估时长: ${enhancement.estimatedDuration}\n推荐理由: ${enhancement.reasoning}`
  };
  
  // 如果有项目建议且用户未指定项目，尝试匹配现有项目
  if (!taskData.projectId && enhancement.projectSuggestion) {
    const projects = await didaClient.getProjects();
    const matchedProject = projects.find(p => 
      p.name.includes(enhancement.projectSuggestion) || 
      enhancement.projectSuggestion.includes(p.name)
    );
    if (matchedProject) {
      enhancedTask.projectId = matchedProject.id;
    }
  }
  
  const result = await didaClient.createTask(enhancedTask);
  
  return {
    success: true,
    data: result,
    aiEnhancement: enhancement,
    message: `任务创建成功！AI建议优先级: ${enhancement.recommendedPriority}`
  };
}

async function listTasks(filters, didaClient) {
  const tasks = await didaClient.getTasks(filters.projectId);
  
  let filteredTasks = tasks.tasks || [];
  
  // 应用过滤条件
  if (filters.status !== undefined) {
    filteredTasks = filteredTasks.filter(task => task.status === filters.status);
  }
  
  if (filters.priority) {
    filteredTasks = filteredTasks.filter(task => task.priority >= filters.priority);
  }
  
  if (filters.dueDate) {
    filteredTasks = filteredTasks.filter(task => 
      task.dueDate && task.dueDate.startsWith(filters.dueDate)
    );
  }
  
  return {
    success: true,
    data: filteredTasks,
    total: filteredTasks.length
  };
}
```

### 6. 统计分析云函数

```javascript
// cloudfunctions/analytics/index.js
'use strict';

const { DidaApiClient } = require('../common/dida-api-client');
const { AIService } = require('../common/ai-service');

exports.main = async (event, context) => {
  const { type, params, authInfo } = event;
  
  try {
    const didaClient = new DidaApiClient(authInfo);
    await didaClient.initialize();
    
    const aiService = new AIService();
    
    switch (type) {
      case 'productivity':
        return await generateProductivityReport(params, didaClient, aiService);
      case 'trends':
        return await analyzeTrends(params, didaClient, aiService);
      case 'insights':
        return await generateInsights(params, didaClient, aiService);
      default:
        throw new Error('不支持的分析类型');
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

async function generateProductivityReport(params, didaClient, aiService) {
  const tasks = await didaClient.getTasks();
  const allTasks = tasks.tasks || [];
  
  // 基础统计
  const stats = {
    total: allTasks.length,
    completed: allTasks.filter(t => t.status === 2).length,
    pending: allTasks.filter(t => t.status === 0).length,
    overdue: allTasks.filter(t => t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 2).length
  };
  
  // 使用AI生成洞察报告
  const aiInsights = await aiService.callAI(`
基于以下任务统计数据，生成一份生产力分析报告：

统计数据：
- 总任务数: ${stats.total}
- 已完成: ${stats.completed}
- 待完成: ${stats.pending}
- 逾期任务: ${stats.overdue}

请分析：
1. 完成率和效率评估
2. 时间管理建议
3. 改进建议
4. 下周重点关注事项

返回JSON格式：
{
  "completionRate": "完成率百分比",
  "efficiency": "效率评级(优秀/良好/一般/需改进)",
  "timeManagementTips": ["建议1", "建议2"],
  "improvements": ["改进点1", "改进点2"],
  "nextWeekFocus": ["重点1", "重点2"],
  "summary": "总结"
}
  `);
  
  return {
    success: true,
    data: {
      statistics: stats,
      aiInsights: JSON.parse(aiInsights.match(/\{[\s\S]*\}/)[0])
    }
  };
}
```

## 部署配置

### 1. uniCloud配置

```javascript
// uniCloud.config.js
module.exports = {
  provider: 'aliyun', // 或 'tencent'
  spaceId: 'your-space-id',
  clientSecret: 'your-client-secret',
  endpoint: 'https://api.bspapp.com'
};
```

### 2. 环境变量配置

```javascript
// cloudfunctions/common/config.js
module.exports = {
  AI_API_KEY: process.env.AI_API_KEY || 'your-ai-api-key',
  AI_BASE_URL: process.env.AI_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
  DIDA_DEFAULT_TOKEN: process.env.DIDA_DEFAULT_TOKEN
};
```

## API接口设计

### 1. 智能意图识别接口

```
POST /intent/analyze
Content-Type: application/json

{
  "userInput": "明天提醒我开会",
  "authInfo": {
    "token": "dida-token"
  }
}

Response:
{
  "success": true,
  "data": {
    "intent": "CREATE_TASK",
    "confidence": 0.95,
    "entities": {
      "taskTitle": "开会",
      "dueDate": "2024-01-15",
      "priority": 3
    },
    "aiEnhancement": {
      "suggestedTags": ["工作", "会议"],
      "estimatedDuration": "1小时"
    }
  }
}
```

### 2. 任务管理接口

```
POST /task/create
{
  "title": "完成项目报告",
  "dueDate": "2024-01-20",
  "priority": 4,
  "authInfo": { "token": "..." }
}

GET /task/list?status=0&priority=3
Headers: X-Dida-Token: your-token

PUT /task/:taskId
{
  "status": 2,
  "authInfo": { "token": "..." }
}
```

## 迁移策略

### 阶段1：基础架构搭建（1-2周）
1. 搭建uniCloud项目结构
2. 实现基础的滴答清单API客户端
3. 集成AI服务（通义千问/文心一言）
4. 实现基础的意图识别功能

### 阶段2：核心功能迁移（2-3周）
1. 迁移任务管理功能（对应task_tools.py）
2. 迁移项目管理功能（对应project_tools.py）
3. 迁移标签管理功能（对应tag_tools.py）
4. 实现AI增强的任务创建和查询

### 阶段3：高级功能实现（2-3周）
1. 迁移统计分析功能（对应analytics_tools.py）
2. 迁移目标管理功能（对应goal_tools.py）
3. 实现AI驱动的数据分析和洞察
4. 优化AI提示词和响应处理

### 阶段4：测试和优化（1-2周）
1. 全面测试各项功能
2. 性能优化和错误处理
3. AI模型调优
4. 文档完善

## 成本估算

### uniCloud费用
- 云函数调用：¥0.0133/万次
- 数据库读写：¥0.015/万次
- 存储空间：¥0.07/GB/月
- CDN流量：¥0.18/GB

### AI服务费用
- 通义千问：¥0.008/千tokens
- 文心一言：¥0.012/千tokens
- 预估月费用：¥50-200（取决于使用量）

## 优势分析

### 技术优势
1. **AI驱动**：智能理解用户意图，无需硬编码规则
2. **Serverless**：自动扩缩容，无需运维
3. **成本优化**：按需付费，降低运营成本
4. **开发效率**：uni-app生态集成，开发部署便捷

### 功能优势
1. **自然语言交互**：用户可以用自然语言描述任务
2. **智能补全**：AI自动补全任务信息和建议
3. **上下文理解**：理解复杂的任务关系和依赖
4. **个性化建议**：基于历史数据提供个性化建议

## 风险评估

### 技术风险
1. **AI服务稳定性**：依赖第三方AI服务
2. **响应延迟**：AI调用可能增加响应时间
3. **成本控制**：AI调用费用需要监控

### 缓解措施
1. **多AI服务备份**：支持多个AI服务提供商
2. **缓存机制**：缓存常见意图识别结果
3. **费用监控**：设置费用告警和限制
4. **降级策略**：AI服务不可用时的备用方案

## 总结

这个重构方案将现有的Python MCP服务完全转换为基于uniCloud的Node.js服务，核心特点是：

1. **AI驱动**：使用大语言模型进行意图识别，替代硬编码规则
2. **云原生**：基于uniCloud serverless架构，自动扩缩容
3. **智能化**：AI增强的任务管理和数据分析
4. **标准化**：提供标准REST API接口

该方案保持了原有的所有业务功能，同时大幅提升了用户体验和系统智能化水平。