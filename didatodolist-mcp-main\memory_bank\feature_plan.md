# 滴答清单MCP服务功能规划文档

## 需求分析
### 核心需求:
- [ ] 目标管理功能(增删改查)
  - 支持阶段性目标、永久性目标和习惯性目标
  - 本地存储目标数据(CSV格式)
- [ ] 目标确认功能
  - 检查用户任务是否与设定目标相匹配
- [ ] 统计分析工具
  - 按周/月统计完成任务数量
  - 按项目统计完成任务
  - 任务关键词分析和词云生成

### 技术约束:
- [ ] 保持与现有滴答清单API集成的兼容性
- [ ] 确保新功能作为MCP工具可被调用
- [ ] 本地数据存储应使用CSV格式
- [ ] 实现时应考虑性能优化(特别是统计分析功能)

## 组件分析
### 受影响组件:
- MCP服务器(mcp_server.py)
  - 变更: 需集成新的工具模块
  - 依赖: 现有的认证机制

- 工具模块
  - 新增: goal_tools.py (目标管理)
  - 新增: analytics_tools.py (统计分析)
  - 依赖: 现有的task_tools.py, project_tools.py, tag_tools.py

- 数据存储
  - 新增: goals.csv (目标数据)
  - 依赖: 无

## 设计决策
### 架构:
- [ ] 采用与现有工具模块相同的模块化设计
- [ ] 目标数据使用本地CSV文件存储
- [ ] 统计分析结果以JSON格式返回
- [ ] 词云生成使用外部库(如wordcloud)

### 算法:
- [ ] 目标与任务匹配算法
  - 基于关键词和相似度计算匹配度
- [ ] 任务统计分析算法
  - 时间范围过滤(本周/本月)
  - 项目分组和计数
- [ ] 关键词提取算法
  - 基于频率和重要性提取关键词

## 实施策略
### 阶段1: 基础结构
1. 创建目标数据模型和存储机制
   - [ ] 定义目标数据结构
   - [ ] 实现CSV文件读写功能
   - [ ] 创建goals.csv默认结构

2. 实现基本目标管理功能
   - [ ] 添加目标
   - [ ] 获取目标列表
   - [ ] 更新目标
   - [ ] 删除目标

### 阶段2: 高级功能
1. 实现统计分析功能
   - [ ] 周期性任务完成统计
   - [ ] 按项目分类的任务统计
   - [ ] 任务完成趋势分析

2. 实现关键词分析功能
   - [ ] 任务名称关键词提取
   - [ ] 词频统计
   - [ ] 输出词云数据

### 阶段3: 目标匹配与集成
1. 实现目标与任务匹配功能
   - [ ] 开发匹配算法
   - [ ] 集成到任务查询工具

2. 完成MCP服务集成
   - [ ] 注册新工具到MCP服务
   - [ ] 更新服务文档

## 测试策略
### 单元测试:
- [ ] 目标数据模型与CSV操作测试
- [ ] 统计分析功能测试
- [ ] 关键词提取和匹配算法测试

### 集成测试:
- [ ] 目标管理与滴答清单API集成测试
- [ ] MCP工具调用测试
- [ ] 端到端功能测试

## 文档计划
- [ ] 新增工具API文档
- [ ] 目标数据格式文档
- [ ] 用户指南更新
- [ ] 开发文档更新

## 创意阶段需求
- 算法设计: 需要(关键词提取与匹配算法)
- 架构设计: 需要(目标数据存储与管理架构)
- UI/UX设计: 不需要(无UI组件) 