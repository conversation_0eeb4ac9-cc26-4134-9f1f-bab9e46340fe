# V1.2 - 动态参数解析实现

## 1. 改动概述

### 1.1 核心目标
- 实现动态参数解析机制，支持复杂的参数引用和数据筛选
- 替换V1.1的模拟工具调用为真实的云函数调用
- 完善执行计划生成，支持复杂的依赖关系处理
- 实现智能数据筛选和上下文更新机制

### 1.2 解决的问题
- **V1.1遗留问题**：无法处理步骤间的动态参数传递
- **核心痛点**：如"查找okr项目下的任务"需要先获取项目ID再查询任务
- **技术提升**：从简单执行升级为智能的多步骤任务处理

### 1.3 基于V1.1的改进
- 利用V1.1建立的执行上下文管理器
- 在现有工具调用基础上增加动态参数解析
- 完善执行计划生成逻辑

## 2. 技术方案

### 2.1 动态参数解析器（借鉴MCP参数处理方式）

```javascript
// 新增：动态参数解析器
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextKey = value.substring(9)
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        return contextValue
      }
      throw new Error(`上下文数据不存在: ${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)
      
      if (!stepResult) {
        throw new Error(`步骤结果不存在: ${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj
    
    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }
      
      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find(item => 
            item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase())
          )
        }
      }
      
      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.projects, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式: ${expression}`)
    }

    const [, dataPath, condition] = match
    const data = await this.resolveDynamicValue(`$${dataPath}`, context)
    
    if (!Array.isArray(data)) {
      throw new Error(`筛选目标必须是数组: ${dataPath}`)
    }

    return this.applyFilter(data, condition)
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件: ${condition}`)
    }

    const [, field, operator, value] = conditionMatch
    
    return array.filter(item => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()
      
      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}
```

### 2.2 智能执行计划生成器

```javascript
// 升级：智能执行计划生成器
class IntelligentExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0
    }

    // 使用AI生成更智能的执行计划
    const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)
    const aiResponse = await this.callAI(analysisPrompt)
    
    try {
      const planData = JSON.parse(aiResponse)
      
      // 构建执行步骤
      for (let i = 0; i < planData.steps.length; i++) {
        const stepData = planData.steps[i]
        const toolConfig = TOOL_REGISTRY[stepData.toolName]
        
        const step = {
          stepId: this.generateUUID(),
          toolName: stepData.toolName,
          description: stepData.description,
          parameters: stepData.parameters,
          dependencies: stepData.dependencies || [],
          status: 'pending',
          retryCount: 0,
          maxRetries: 3,
          executionTime: null,
          estimatedTime: toolConfig?.metadata?.estimatedTime || 2000,
          error: null
        }

        executionPlan.steps.push(step)
        executionPlan.estimatedTotalTime += step.estimatedTime
      }

      executionPlan.totalSteps = executionPlan.steps.length
      return executionPlan
      
    } catch (error) {
      console.warn('AI执行计划解析失败，使用默认计划:', error)
      return this.generateDefaultPlan(userInput, intentType)
    }
  }

  static buildAnalysisPrompt(userInput, intentType) {
    const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
    
    return `分析用户输入："${userInput}"
意图类型：${intentType}

${toolPrompt}

请生成执行计划，支持以下动态参数引用：
- $context.key: 引用上下文数据
- $step.stepId.path: 引用前置步骤的结果
- $filter(stepId.path, condition): 对数据进行筛选

返回JSON格式：
{
  "analysis": "用户意图分析",
  "steps": [
    {
      "toolName": "工具名称",
      "description": "步骤描述", 
      "parameters": {
        "param1": "静态值",
        "param2": "$context.targetProject.id"
      },
      "dependencies": ["step1"],
      "reasoning": "选择此工具的原因"
    }
  ]
}`
  }

  static async callAI(prompt) {
    // 调用豆包AI生成执行计划
    const openai = new OpenAI(doubaoParams)
    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt }
      ],
      model: 'doubao-seed-1-6-250615',
      stream: false,
      timeout: 30000
    })

    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    // 当AI生成失败时的默认计划
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0
    }

    // 基于规则的默认计划生成
    if (intentType === 'find_task') {
      if (userInput.includes('项目') || userInput.includes('project')) {
        executionPlan.steps = [
          {
            stepId: this.generateUUID(),
            toolName: 'getProjects',
            description: '获取项目列表',
            parameters: { filter: this.extractProjectKeyword(userInput) },
            dependencies: [],
            status: 'pending',
            estimatedTime: 1500
          },
          {
            stepId: this.generateUUID(),
            toolName: 'getTasks',
            description: '获取项目下的任务',
            parameters: { 
              projectId: '$context.targetProject.id',
              completed: false 
            },
            dependencies: [executionPlan.steps[0]?.stepId],
            status: 'pending',
            estimatedTime: 2000
          }
        ]
        executionPlan.estimatedTotalTime = 3500
      }
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }
}
```

### 2.3 真实工具调用引擎

```javascript
// 升级：真实工具调用引擎
async function executeIntelligentPlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime
      },
      timestamp: Date.now()
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime
        },
        timestamp: Date.now()
      })

      step.status = 'executing'
      const stepStartTime = Date.now()

      try {
        // V1.2核心：动态参数解析
        const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)
        
        // 参数验证
        const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)
        
        // 真实工具调用
        const result = await callRealTool(step.toolName, validatedParams)
        
        // 存储结果到上下文
        context.setStepResult(step.stepId, result)
        
        step.status = 'completed'
        step.executionTime = Date.now() - stepStartTime

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          executionTime: step.executionTime,
          contextUpdates: this.getContextUpdates(context),
          timestamp: Date.now()
        })

      } catch (error) {
        step.executionTime = Date.now() - stepStartTime
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          executionTime: step.executionTime,
          timestamp: Date.now()
        })

        // V1.2版本：简单的错误处理，V1.3版本会完善
        throw error
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: this.generateExecutionSummary(executionPlan, context),
      timestamp: Date.now()
    })

    return executionPlan

  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now()
    })
    
    throw error
  }
}

// 真实工具调用函数
async function callRealTool(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  try {
    // 调用对应的云函数
    const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
    const result = await cloudFunction[toolConfig.method](parameters)
    
    return result
    
  } catch (error) {
    throw new Error(`工具执行失败：${error.message}`)
  }
}

// 获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// 生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter(s => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter(s => s.status === 'failed')
  
  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime: completedSteps.length > 0 
      ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
      : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0
  }
}
```

## 3. chatStreamSSE函数升级

### 3.1 主要修改点

```javascript
// 将V1.1的SimpleExecutionPlanner替换为IntelligentExecutionPlanner
// 将executeSimplePlan替换为executeIntelligentPlan

async chatStreamSSE(params) {
  // ... 现有逻辑保持不变 ...

  // 修改：使用智能执行计划生成器
  if (intentType && intentType !== 'chat') {
    const context = new ExecutionContextManager(generateUUID(), message)
    
    // 使用智能执行计划生成器
    const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)
    
    if (executionPlan.totalSteps > 0) {
      // 使用智能执行引擎
      await executeIntelligentPlan(executionPlan, context, sseChannel)
      
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'task_executed',
          intentType: intentType,
          executionPlan: executionPlan,
          contextData: Array.from(context.contextData.keys()),
          executionTime: executionPlan.totalExecutionTime
        }
      }
    }
  }

  // ... 其他逻辑保持不变 ...
}
```

## 4. 测试用例

### 4.1 动态参数解析测试
```javascript
test('动态参数解析', async () => {
  const context = new ExecutionContextManager('session-1', '查看okr项目任务')
  context.setContextData('targetProject', { id: 'proj-1', name: 'OKR项目' })
  
  const step = {
    parameters: { projectId: '$context.targetProject.id', completed: false },
    dependencies: []
  }
  
  const resolved = await DynamicParameterResolver.resolveParameters(step, context)
  expect(resolved.projectId).toBe('proj-1')
})
```

### 4.2 筛选表达式测试
```javascript
test('筛选表达式处理', async () => {
  const context = new ExecutionContextManager('session-1', 'test')
  context.setStepResult('step1', {
    projects: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' }
    ]
  })
  
  const result = await DynamicParameterResolver.processFilterExpression(
    '$filter(step1.projects, name contains "okr")', 
    context
  )
  
  expect(result).toHaveLength(1)
  expect(result[0].name).toBe('OKR项目')
})
```

### 4.3 完整流程测试
```javascript
test('完整执行流程', async () => {
  const params = {
    message: '查看okr项目下的未完成任务',
    channel: mockSSEChannel
  }
  
  const result = await chatStreamSSE(params)
  expect(result.errCode).toBe(0)
  expect(result.data.type).toBe('task_executed')
  expect(result.data.executionPlan.steps).toHaveLength(2)
})
```

## 5. 部署指南

### 5.1 前置条件
- V1.1版本已稳定运行
- 滴答清单API云函数已部署
- 测试环境已准备就绪

### 5.2 部署步骤
1. **代码集成**：集成动态参数解析器和智能执行计划生成器
2. **API测试**：验证真实工具调用功能
3. **参数解析测试**：验证各种动态参数引用场景
4. **性能测试**：确保响应时间在可接受范围内
5. **灰度发布**：30%用户先行体验
6. **监控观察**：密切关注错误率和性能指标
7. **全量发布**：确认稳定后全量发布

## 6. 风险评估

### 6.1 技术风险
- **风险**：动态参数解析可能出现解析错误
- **应对**：完善的错误处理和降级机制

### 6.2 性能风险
- **风险**：真实API调用可能增加响应时间
- **应对**：设置合理的超时时间和并发控制

### 6.3 稳定性风险
- **风险**：外部API依赖可能影响系统稳定性
- **应对**：V1.3版本将完善重试和降级机制

## 7. 下一版本预告

V1.3版本将在V1.2的基础上：
- 完善分层错误处理机制
- 实现智能重试和降级策略
- 添加性能监控和指标收集
- 优化执行效率和用户体验

V1.2版本建立的动态参数解析能力将为V1.3版本的高级错误处理提供完整的技术基础。
